'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth/AuthProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import { Eye, EyeOff, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { buildApiUrl, authenticatedFetch } from '@/lib/api-client'

interface LoginFormProps {
  onSuccess?: () => void
  redirectTo?: string
}

export function LoginForm({ onSuccess, redirectTo }: LoginFormProps) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [mounted, setMounted] = useState(false)

  // Try to get auth context, but handle if it fails
  let signIn: ((email: string, password: string) => Promise<{ error: any }>) | null = null
  try {
    const auth = useAuth()
    signIn = auth.signIn
  } catch (error) {
    console.error('Failed to get auth context:', error)
  }

  // Prevent hydration issues
  useEffect(() => {
    console.log('LoginForm component mounted!')
    setMounted(true)
  }, [])



  const handleSubmit = async (e: React.FormEvent) => {
    console.log('handleSubmit function called!')
    e.preventDefault()
    
    if (!email || !password) {
      toast.error('Please fill in all fields')
      return
    }

    setLoading(true)
    
    try {
      console.log('Starting login process...')
      
      // First, validate the login attempt with the API for security checks
      console.log('Making API validation request...')
      const validationResponse = await authenticatedFetch(buildApiUrl('/auth/validate-login'), {
        method: 'POST',
        requireAuth: false, // This is a pre-auth validation
        body: JSON.stringify({ email, password }),
      })
      console.log('API validation response received:', validationResponse.status)

      if (!validationResponse.ok) {
        const errorData = await validationResponse.json()
        if (validationResponse.status === 429) {
          // Rate limited or brute force protection
          const detail = errorData.detail
          if (typeof detail === 'object' && detail.message) {
            toast.error(detail.message)
          } else {
            toast.error('Too many failed attempts. Please try again later.')
          }
          return
        }
        throw new Error('Validation failed')
      }

      // Proceed with Supabase authentication
      console.log('Attempting Supabase authentication...')
      if (!signIn) {
        throw new Error('Authentication service not available')
      }
      const { error } = await signIn(email, password)
      console.log('Supabase authentication result:', { error })
      
      if (error) {
        console.error('Authentication error:', error)
        toast.error(error.message || 'Failed to sign in')
      } else {
        console.log('Authentication successful, proceeding with success flow...')
        // Record successful login for security tracking
        try {
          await authenticatedFetch(buildApiUrl('/auth/login-success'), {
            method: 'POST',
            requireAuth: true,
            body: JSON.stringify({ email }),
          })
        } catch (recordError) {
          // Don't fail the login if recording fails
          console.warn('Failed to record login success:', recordError)
        }

        toast.success('Signed in successfully!')
        onSuccess?.()
        if (redirectTo) {
          window.location.href = redirectTo
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      console.error('Login error stack:', error instanceof Error ? error.stack : 'No stack available')
      toast.error('An unexpected error occurred: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setLoading(false)
    }
  }

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Sign In</CardTitle>
          <CardDescription>Loading...</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold">Sign In</CardTitle>
        <CardDescription>
          Enter your email and password to access your account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form
          onSubmit={(e) => {
            console.log('Form onSubmit triggered!')
            handleSubmit(e)
          }}
          className="space-y-4"
        >
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => {
                console.log('Email input changed:', e.target.value)
                setEmail(e.target.value)
              }}
              disabled={loading}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                value={password}
                onChange={(e) => {
                  console.log('Password input changed')
                  setPassword(e.target.value)
                }}
                disabled={loading}
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => {
                  console.log('Show password button clicked!')
                  setShowPassword(!showPassword)
                }}
                disabled={loading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>



          <Button
            type="submit"
            className="w-full"
            disabled={loading}
            onClick={(e) => {
              console.log('Button clicked directly!')
              // Don't prevent default here since we want form submission
            }}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Signing in...
              </>
            ) : (
              'Sign In'
            )}
          </Button>
        </form>

        <div className="mt-4 text-center space-y-2">
          <Link
            href="/auth/forgot-password"
            className="text-sm text-muted-foreground hover:text-primary underline-offset-4 hover:underline"
          >
            Forgot your password?
          </Link>
          <div className="text-sm text-muted-foreground">
            Don't have an account?{' '}
            <Link
              href="/auth/signup"
              className="text-primary hover:underline underline-offset-4"
            >
              Sign up
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}